# 数据模型设计开发文档（修正版）

## 1. 功能需求描述

### 1.1 业务背景
基于实际RS485通讯架构的IoT监控系统，准确反映空调机组的真实通讯模式：
- **云端监控系统**：运行在服务器上的ASP.NET Core应用
- **本地数据推送**：Windows服务将现场串口数据推送到云端
- **设备拓扑结构**：机组 -> 主外机 -> 子设备（内机、外机、控制器）
- **通讯模式**：主外机轮询点播，子设备响应

### 1.2 功能范围
- 正确表示主从设备关系（主外机 -> 子设备）
- 动态识别实际存在的设备（有回复的地址）
- 区分设备类型（外机、内机、控制器）
- 存储轮询周期内重要的通讯数据
- 支持设备在线/离线状态检测

### 1.3 通讯协议格式
```
RS485协议帧格式：
┌──────┬──────┬──────┬──────┬──────┬──────────┬──────┐
│ 帧头 │ 源地址│ 目标地址│ 命令码│ 数据长度│  数据内容  │ CRC校验│
│ 7E   │ 1字节 │ 1字节  │ 1字节 │ 1字节  │  N字节   │ 2字节 │
└──────┴──────┴──────┴──────┴──────┴──────────┴──────┘

地址说明：
- F1: 主外机（主设备）
- F0/F2/F3等: 子外机
- 00-FF: 具体设备地址（内机、控制器等）
```

## 2. 技术实现方案

### 2.1 架构设计
```
数据访问层 (Data Access Layer)
├── DbContext/                    # Entity Framework 上下文
│   └── AirMonitorDbContext.cs   # 主数据库上下文
├── Entities/                    # 实体模型
│   ├── Unit/                    # 机组相关实体
│   ├── Device/                  # 设备相关实体
│   ├── Communication/           # 通讯相关实体
│   ├── Monitoring/              # 监控数据实体
│   └── User/                    # 用户管理实体
├── Configurations/              # 实体配置
└── Migrations/                  # 数据库迁移
```

### 2.2 技术选型
- **.NET 版本**：.NET 8.0
- **ORM 框架**：Entity Framework Core 8.0
- **数据库**：SQL Server
- **通讯协议**：RS485（7E帧头格式）
- **实时通讯**：SignalR
- **日志框架**：Serilog

## 3. 数据模型定义（重新设计）

### 3.1 核心实体设计

#### 3.1.1 机组实体 (AirConditioningUnit) - 设备拓扑根节点
```csharp
public class AirConditioningUnit : BaseEntity
{
    public string UnitName { get; set; }           // 机组名称（用户自定义）
    public string UnitCode { get; set; }           // 机组编码（唯一标识）
    public string Location { get; set; }           // 安装位置
    public string Description { get; set; }        // 机组描述
    
    // 通讯配置
    public string SerialPortSource { get; set; }   // 数据来源标识（Windows服务推送标识）
    public DateTime? LastDataReceived { get; set; } // 最后接收数据时间
    public bool IsOnline { get; set; }             // 机组在线状态
    
    // 主设备信息
    public byte? MasterDeviceAddress { get; set; } // 主外机地址（F1等）
    public int? MasterDeviceId { get; set; }       // 主设备ID
    public Device MasterDevice { get; set; }       // 主设备导航属性
    
    // 导航属性
    public ICollection<Device> Devices { get; set; }           // 所有设备
    public ICollection<CommunicationLog> CommunicationLogs { get; set; } // 通讯日志
}
```

#### 3.1.2 设备实体 (Device) - 重新设计
```csharp
public class Device : BaseEntity
{
    // 设备标识
    public byte DeviceAddress { get; set; }        // RS485设备地址（0x00-0xFF）
    public string DeviceName { get; set; }         // 设备名称（用户可自定义）
    
    // 设备类型和角色
    public DeviceType DeviceType { get; set; }     // 设备类型
    public DeviceRole DeviceRole { get; set; }     // 设备角色（主设备/从设备）
    
    // 设备信息（通过通讯获取）
    public string ModelCode { get; set; }          // 机型代码
    public string FirmwareVersion { get; set; }    // 固件版本
    public string SerialNumber { get; set; }       // 设备序列号
    
    // 通讯状态
    public DateTime? FirstDiscoveredTime { get; set; }   // 首次发现时间
    public DateTime? LastCommunicationTime { get; set; } // 最后通讯时间
    public DateTime? LastResponseTime { get; set; }      // 最后响应时间
    public bool IsOnline { get; set; }                   // 在线状态
    public int CommunicationFailureCount { get; set; }   // 连续通讯失败次数
    
    // 关联关系
    public int AirConditioningUnitId { get; set; } // 所属机组ID
    public AirConditioningUnit AirConditioningUnit { get; set; } // 所属机组
    
    public int? DeviceModelId { get; set; }        // 设备型号ID（可选）
    public DeviceModel DeviceModel { get; set; }   // 设备型号模板
    
    // 主从关系
    public int? MasterDeviceId { get; set; }       // 主设备ID（从设备指向主设备）
    public Device MasterDevice { get; set; }       // 主设备
    public ICollection<Device> SlaveDevices { get; set; } // 从设备列表
    
    // 导航属性
    public ICollection<DeviceParameterValue> ParameterValues { get; set; }
    public ICollection<CommunicationLog> CommunicationLogs { get; set; }
    public ICollection<AlarmRecord> AlarmRecords { get; set; }
}

public enum DeviceType
{
    OutdoorUnit = 1,        // 外机
    IndoorUnit = 2,         // 内机
    WiredController = 3,    // 线控器
    CentralController = 4,  // 集控器
    TemperatureSensor = 5,  // 温度传感器
    PressureSensor = 6,     // 压力传感器
    Other = 99              // 其他设备
}

public enum DeviceRole
{
    Master = 1,             // 主设备（主外机，负责轮询）
    Slave = 2               // 从设备（响应轮询）
}
```

#### 3.1.3 通讯日志实体 (CommunicationLog) - 新增
```csharp
public class CommunicationLog : BaseEntity
{
    // 基本信息
    public DateTime Timestamp { get; set; }        // 通讯时间戳
    public CommunicationDirection Direction { get; set; } // 通讯方向
    public CommunicationType Type { get; set; }    // 通讯类型
    
    // 协议信息
    public byte FrameHeader { get; set; }          // 帧头（7E）
    public byte SourceAddress { get; set; }        // 源地址
    public byte TargetAddress { get; set; }        // 目标地址
    public byte CommandCode { get; set; }          // 命令码
    public byte DataLength { get; set; }           // 数据长度
    public byte[] DataContent { get; set; }        // 数据内容
    public ushort CrcValue { get; set; }           // CRC校验值
    
    // 原始数据
    public byte[] RawData { get; set; }            // 原始帧数据
    public string HexString { get; set; }          // 十六进制字符串
    
    // 解析结果
    public bool IsValidFrame { get; set; }         // 帧格式是否有效
    public bool IsCrcValid { get; set; }           // CRC校验是否通过
    public string ParsedData { get; set; }         // 解析后的数据（JSON格式）
    public string ErrorMessage { get; set; }       // 错误信息
    
    // 关联关系
    public int AirConditioningUnitId { get; set; } // 所属机组ID
    public AirConditioningUnit AirConditioningUnit { get; set; } // 所属机组
    
    public int? DeviceId { get; set; }             // 相关设备ID
    public Device Device { get; set; }             // 相关设备
}

public enum CommunicationDirection
{
    MasterToSlave = 1,      // 主设备到从设备（轮询）
    SlaveToMaster = 2,      // 从设备到主设备（响应）
    SystemReceived = 3      // 系统接收到的数据
}

public enum CommunicationType
{
    ParameterRead = 1,      // 参数读取
    ParameterWrite = 2,     // 参数写入
    StatusQuery = 3,        // 状态查询
    AlarmQuery = 4,         // 告警查询
    DeviceInfo = 5,         // 设备信息
    Heartbeat = 6,          // 心跳检测
    Other = 99              // 其他类型
}
```

#### 3.1.4 RS485协议帧实体 (Rs485Frame) - 新增
```csharp
public class Rs485Frame
{
    public byte FrameHeader { get; set; }          // 帧头（固定7E）
    public byte SourceAddress { get; set; }        // 源地址（1字节）
    public byte TargetAddress { get; set; }        // 目标地址（1字节）
    public byte CommandCode { get; set; }          // 命令码（1字节）
    public byte DataLength { get; set; }           // 数据长度（1字节）
    public byte[] DataContent { get; set; }        // 数据内容（N字节）
    public ushort CrcValue { get; set; }           // CRC校验（2字节）

    // 辅助属性
    public byte[] RawData { get; set; }            // 完整原始数据
    public bool IsValid { get; set; }              // 帧是否有效
    public string ErrorMessage { get; set; }       // 错误信息

    // 解析方法
    public static Rs485Frame Parse(byte[] data);
    public byte[] ToByteArray();
    public string ToHexString();
    public bool ValidateCrc();
}
```

### 3.2 设备型号和参数实体

#### 3.2.1 设备型号模板 (DeviceModel)
```csharp
public class DeviceModel : BaseEntity
{
    public string ModelCode { get; set; }          // 机型代码
    public string ModelName { get; set; }          // 机型名称
    public DeviceType DeviceType { get; set; }     // 设备类型
    public string Manufacturer { get; set; }       // 制造商
    public string Description { get; set; }        // 描述
    public bool IsActive { get; set; }             // 是否启用

    // RS485协议配置
    public string SupportedCommands { get; set; }  // 支持的命令码列表（JSON格式）
    public int PollingInterval { get; set; }       // 轮询间隔（毫秒）
    public int ResponseTimeout { get; set; }       // 响应超时（毫秒）

    // 导航属性
    public ICollection<DeviceParameter> Parameters { get; set; }
    public ICollection<Device> Devices { get; set; }
}
```

#### 3.2.2 设备参数定义 (DeviceParameter)
```csharp
public class DeviceParameter : BaseEntity
{
    public int DeviceModelId { get; set; }         // 设备型号ID
    public DeviceModel DeviceModel { get; set; }   // 设备型号

    public string ParameterCode { get; set; }      // 参数代码
    public string ParameterName { get; set; }      // 参数名称
    public string DisplayName { get; set; }        // 显示名称
    public ParameterDataType DataType { get; set; } // 数据类型
    public string Unit { get; set; }               // 单位
    public ParameterCategory Category { get; set; } // 参数分类

    // RS485通讯配置
    public byte CommandCode { get; set; }          // 命令码
    public byte ParameterIndex { get; set; }       // 参数索引（在数据区中的位置）
    public ParameterFormat Format { get; set; }    // 参数格式
    public int ByteLength { get; set; }            // 数据长度（字节）
    public int BitPosition { get; set; }           // 位位置（用于位字段）

    // 数据验证
    public decimal? MinValue { get; set; }          // 最小值
    public decimal? MaxValue { get; set; }          // 最大值
    public string ValidValues { get; set; }        // 有效值列表（JSON格式）
    public string DefaultValue { get; set; }       // 默认值

    // 显示配置
    public int DisplayOrder { get; set; }          // 显示顺序
    public bool IsVisible { get; set; }            // 是否显示
    public bool IsReadOnly { get; set; }           // 是否只读
    public bool IsAlarmParameter { get; set; }     // 是否告警参数

    // 导航属性
    public ICollection<DeviceParameterValue> ParameterValues { get; set; }
}

public enum ParameterDataType
{
    Integer = 1,        // 整数
    Decimal = 2,        // 小数
    Boolean = 3,        // 布尔值
    String = 4,         // 字符串
    Enum = 5,          // 枚举值
    BitField = 6       // 位字段
}

public enum ParameterCategory
{
    Temperature = 1,    // 温度参数
    Pressure = 2,       // 压力参数
    Status = 3,         // 状态参数
    Control = 4,        // 控制参数
    Alarm = 5,          // 告警参数
    Configuration = 6,  // 配置参数
    Statistics = 7      // 统计参数
}

public enum ParameterFormat
{
    SingleValue = 1,    // 单一数值（2字节整数）
    BitField = 2,       // 位字段（16个布尔状态位）
    ByteCombination = 3,// 字节组合（高字节+低字节不同含义）
    MixedFormat = 4,    // 混合格式（部分位状态+部分位数值）
    EnumValue = 5,      // 枚举类型
    StringValue = 6     // 字符串类型
}
```

### 3.3 监控数据实体

#### 3.3.1 设备参数值 (DeviceParameterValue) - 实时数据
```csharp
public class DeviceParameterValue : BaseEntity
{
    public int DeviceId { get; set; }              // 设备ID
    public Device Device { get; set; }             // 设备

    public int DeviceParameterId { get; set; }     // 设备参数ID
    public DeviceParameter DeviceParameter { get; set; } // 设备参数定义

    public DateTime Timestamp { get; set; }        // 时间戳
    public string Value { get; set; }              // 参数值（字符串存储）
    public decimal? NumericValue { get; set; }     // 数值（用于计算和查询）
    public bool? BooleanValue { get; set; }        // 布尔值

    // 数据质量
    public DataQuality Quality { get; set; }       // 数据质量
    public string Source { get; set; }             // 数据来源
    public bool IsAlarm { get; set; }              // 是否告警

    // 通讯相关
    public int? CommunicationLogId { get; set; }   // 关联的通讯日志ID
    public CommunicationLog CommunicationLog { get; set; } // 关联的通讯日志
}

public enum DataQuality
{
    Good = 1,           // 良好
    Uncertain = 2,      // 不确定
    Bad = 3,            // 坏值
    Timeout = 4,        // 超时
    Error = 5           // 错误
}
```

### 3.4 设备发现和状态管理

#### 3.4.1 设备发现日志 (DeviceDiscoveryLog)
```csharp
public class DeviceDiscoveryLog : BaseEntity
{
    public DateTime DiscoveryTime { get; set; }    // 发现时间
    public int AirConditioningUnitId { get; set; } // 机组ID
    public AirConditioningUnit AirConditioningUnit { get; set; } // 机组

    public byte DeviceAddress { get; set; }        // 设备地址
    public DeviceType? DeviceType { get; set; }    // 设备类型
    public string ModelCode { get; set; }          // 机型代码
    public string FirmwareVersion { get; set; }    // 固件版本

    public DiscoveryResult Result { get; set; }    // 发现结果
    public string ResultMessage { get; set; }      // 结果消息
    public bool IsNewDevice { get; set; }          // 是否新设备

    public int? DeviceId { get; set; }             // 关联的设备ID
    public Device Device { get; set; }             // 关联的设备
}

public enum DiscoveryResult
{
    Success = 1,        // 成功发现
    NoResponse = 2,     // 无响应
    InvalidResponse = 3,// 无效响应
    CrcError = 4,       // CRC错误
    Timeout = 5,        // 超时
    Error = 6           // 其他错误
}
```

## 4. 数据库设计

### 4.1 表结构设计
```sql
-- 机组表
CREATE TABLE AirConditioningUnits (
    Id int IDENTITY(1,1) PRIMARY KEY,
    UnitName nvarchar(100) NOT NULL,
    UnitCode nvarchar(50) NOT NULL UNIQUE,
    Location nvarchar(200),
    Description nvarchar(500),
    SerialPortSource nvarchar(100),
    LastDataReceived datetime2,
    IsOnline bit NOT NULL DEFAULT 0,
    MasterDeviceAddress tinyint,
    MasterDeviceId int,
    CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
    IsDeleted bit NOT NULL DEFAULT 0
);

-- 设备表
CREATE TABLE Devices (
    Id int IDENTITY(1,1) PRIMARY KEY,
    DeviceAddress tinyint NOT NULL,
    DeviceName nvarchar(100),
    DeviceType int NOT NULL,
    DeviceRole int NOT NULL,
    ModelCode nvarchar(50),
    FirmwareVersion nvarchar(20),
    SerialNumber nvarchar(50),
    FirstDiscoveredTime datetime2,
    LastCommunicationTime datetime2,
    LastResponseTime datetime2,
    IsOnline bit NOT NULL DEFAULT 0,
    CommunicationFailureCount int NOT NULL DEFAULT 0,
    AirConditioningUnitId int NOT NULL,
    DeviceModelId int,
    MasterDeviceId int,
    CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
    IsDeleted bit NOT NULL DEFAULT 0,

    CONSTRAINT FK_Devices_AirConditioningUnits
        FOREIGN KEY (AirConditioningUnitId) REFERENCES AirConditioningUnits(Id),
    CONSTRAINT FK_Devices_DeviceModels
        FOREIGN KEY (DeviceModelId) REFERENCES DeviceModels(Id),
    CONSTRAINT FK_Devices_MasterDevice
        FOREIGN KEY (MasterDeviceId) REFERENCES Devices(Id),
    CONSTRAINT UQ_Devices_Address_Unit
        UNIQUE (DeviceAddress, AirConditioningUnitId)
);

-- 通讯日志表
CREATE TABLE CommunicationLogs (
    Id bigint IDENTITY(1,1) PRIMARY KEY,
    Timestamp datetime2 NOT NULL,
    Direction int NOT NULL,
    Type int NOT NULL,
    FrameHeader tinyint NOT NULL,
    SourceAddress tinyint NOT NULL,
    TargetAddress tinyint NOT NULL,
    CommandCode tinyint NOT NULL,
    DataLength tinyint NOT NULL,
    DataContent varbinary(255),
    CrcValue smallint NOT NULL,
    RawData varbinary(300),
    HexString nvarchar(600),
    IsValidFrame bit NOT NULL,
    IsCrcValid bit NOT NULL,
    ParsedData nvarchar(max),
    ErrorMessage nvarchar(500),
    AirConditioningUnitId int NOT NULL,
    DeviceId int,
    CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),

    CONSTRAINT FK_CommunicationLogs_AirConditioningUnits
        FOREIGN KEY (AirConditioningUnitId) REFERENCES AirConditioningUnits(Id),
    CONSTRAINT FK_CommunicationLogs_Devices
        FOREIGN KEY (DeviceId) REFERENCES Devices(Id)
);

-- 设备发现日志表
CREATE TABLE DeviceDiscoveryLogs (
    Id bigint IDENTITY(1,1) PRIMARY KEY,
    DiscoveryTime datetime2 NOT NULL,
    AirConditioningUnitId int NOT NULL,
    DeviceAddress tinyint NOT NULL,
    DeviceType int,
    ModelCode nvarchar(50),
    FirmwareVersion nvarchar(20),
    Result int NOT NULL,
    ResultMessage nvarchar(500),
    IsNewDevice bit NOT NULL,
    DeviceId int,
    CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),

    CONSTRAINT FK_DeviceDiscoveryLogs_AirConditioningUnits
        FOREIGN KEY (AirConditioningUnitId) REFERENCES AirConditioningUnits(Id),
    CONSTRAINT FK_DeviceDiscoveryLogs_Devices
        FOREIGN KEY (DeviceId) REFERENCES Devices(Id)
);
```

### 4.2 索引设计
```sql
-- 设备查询索引
CREATE INDEX IX_Devices_Address ON Devices(DeviceAddress);
CREATE INDEX IX_Devices_Unit_Online ON Devices(AirConditioningUnitId, IsOnline);
CREATE INDEX IX_Devices_LastCommunication ON Devices(LastCommunicationTime);
CREATE INDEX IX_Devices_Master ON Devices(MasterDeviceId);

-- 通讯日志索引
CREATE INDEX IX_CommunicationLogs_Timestamp ON CommunicationLogs(Timestamp);
CREATE INDEX IX_CommunicationLogs_Unit_Time ON CommunicationLogs(AirConditioningUnitId, Timestamp);
CREATE INDEX IX_CommunicationLogs_Device_Time ON CommunicationLogs(DeviceId, Timestamp);
CREATE INDEX IX_CommunicationLogs_Address ON CommunicationLogs(SourceAddress, TargetAddress);

-- 设备发现日志索引
CREATE INDEX IX_DeviceDiscoveryLogs_Time ON DeviceDiscoveryLogs(DiscoveryTime);
CREATE INDEX IX_DeviceDiscoveryLogs_Unit_Address ON DeviceDiscoveryLogs(AirConditioningUnitId, DeviceAddress);

-- 参数值索引
CREATE INDEX IX_DeviceParameterValues_Device_Time ON DeviceParameterValues(DeviceId, Timestamp);
CREATE INDEX IX_DeviceParameterValues_Parameter_Time ON DeviceParameterValues(DeviceParameterId, Timestamp);
```

## 5. Entity Framework 配置

### 5.1 DbContext 配置
```csharp
public class AirMonitorDbContext : DbContext
{
    // 核心实体
    public DbSet<AirConditioningUnit> AirConditioningUnits { get; set; }
    public DbSet<Device> Devices { get; set; }
    public DbSet<DeviceModel> DeviceModels { get; set; }
    public DbSet<DeviceParameter> DeviceParameters { get; set; }

    // 通讯相关
    public DbSet<CommunicationLog> CommunicationLogs { get; set; }
    public DbSet<DeviceDiscoveryLog> DeviceDiscoveryLogs { get; set; }

    // 监控数据
    public DbSet<DeviceParameterValue> DeviceParameterValues { get; set; }
    public DbSet<AlarmRecord> AlarmRecords { get; set; }

    // 用户管理
    public DbSet<User> Users { get; set; }
    public DbSet<Role> Roles { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // 应用所有实体配置
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(AirMonitorDbContext).Assembly);

        // 全局查询过滤器（软删除）
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            if (typeof(BaseEntity).IsAssignableFrom(entityType.ClrType))
            {
                modelBuilder.Entity(entityType.ClrType)
                    .HasQueryFilter(GetSoftDeleteFilter(entityType.ClrType));
            }
        }

        // 配置时序数据分区
        ConfigurePartitioning(modelBuilder);
    }

    private void ConfigurePartitioning(ModelBuilder modelBuilder)
    {
        // 通讯日志表按月分区
        modelBuilder.Entity<CommunicationLog>()
            .ToTable("CommunicationLogs", t => t.IsPartitioned());

        // 参数值表按月分区
        modelBuilder.Entity<DeviceParameterValue>()
            .ToTable("DeviceParameterValues", t => t.IsPartitioned());
    }
}
```

### 5.2 实体配置类
```csharp
// 机组配置
public class AirConditioningUnitConfiguration : IEntityTypeConfiguration<AirConditioningUnit>
{
    public void Configure(EntityTypeBuilder<AirConditioningUnit> builder)
    {
        builder.ToTable("AirConditioningUnits");

        builder.Property(e => e.UnitName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(e => e.UnitCode)
            .IsRequired()
            .HasMaxLength(50);

        builder.HasIndex(e => e.UnitCode)
            .IsUnique();

        builder.Property(e => e.SerialPortSource)
            .HasMaxLength(100);

        // 配置主设备关系
        builder.HasOne(e => e.MasterDevice)
            .WithMany()
            .HasForeignKey(e => e.MasterDeviceId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}

// 设备配置
public class DeviceConfiguration : IEntityTypeConfiguration<Device>
{
    public void Configure(EntityTypeBuilder<Device> builder)
    {
        builder.ToTable("Devices");

        builder.Property(e => e.DeviceAddress)
            .IsRequired();

        builder.Property(e => e.DeviceName)
            .HasMaxLength(100);

        builder.Property(e => e.ModelCode)
            .HasMaxLength(50);

        // 复合唯一索引：同一机组内设备地址唯一
        builder.HasIndex(e => new { e.DeviceAddress, e.AirConditioningUnitId })
            .IsUnique();

        // 配置机组关系
        builder.HasOne(e => e.AirConditioningUnit)
            .WithMany(u => u.Devices)
            .HasForeignKey(e => e.AirConditioningUnitId)
            .OnDelete(DeleteBehavior.Cascade);

        // 配置主从设备关系
        builder.HasOne(e => e.MasterDevice)
            .WithMany(d => d.SlaveDevices)
            .HasForeignKey(e => e.MasterDeviceId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}

// 通讯日志配置
public class CommunicationLogConfiguration : IEntityTypeConfiguration<CommunicationLog>
{
    public void Configure(EntityTypeBuilder<CommunicationLog> builder)
    {
        builder.ToTable("CommunicationLogs");

        builder.Property(e => e.Timestamp)
            .IsRequired();

        builder.Property(e => e.DataContent)
            .HasMaxLength(255);

        builder.Property(e => e.RawData)
            .HasMaxLength(300);

        builder.Property(e => e.HexString)
            .HasMaxLength(600);

        builder.Property(e => e.ParsedData)
            .HasColumnType("nvarchar(max)");

        // 配置关系
        builder.HasOne(e => e.AirConditioningUnit)
            .WithMany(u => u.CommunicationLogs)
            .HasForeignKey(e => e.AirConditioningUnitId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.Device)
            .WithMany(d => d.CommunicationLogs)
            .HasForeignKey(e => e.DeviceId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}
```

## 6. 通讯协议解析服务

### 6.1 RS485协议解析器
```csharp
public class Rs485ProtocolParser
{
    private const byte FRAME_HEADER = 0x7E;
    private const int MIN_FRAME_LENGTH = 7; // 最小帧长度

    public static Rs485Frame ParseFrame(byte[] data)
    {
        var frame = new Rs485Frame { RawData = data };

        try
        {
            // 验证最小长度
            if (data.Length < MIN_FRAME_LENGTH)
            {
                frame.IsValid = false;
                frame.ErrorMessage = "数据长度不足";
                return frame;
            }

            // 验证帧头
            if (data[0] != FRAME_HEADER)
            {
                frame.IsValid = false;
                frame.ErrorMessage = "帧头错误";
                return frame;
            }

            // 解析帧结构
            frame.FrameHeader = data[0];
            frame.SourceAddress = data[1];
            frame.TargetAddress = data[2];
            frame.CommandCode = data[3];
            frame.DataLength = data[4];

            // 验证数据长度
            int expectedLength = 5 + frame.DataLength + 2; // 头部5字节 + 数据 + CRC2字节
            if (data.Length != expectedLength)
            {
                frame.IsValid = false;
                frame.ErrorMessage = $"数据长度不匹配，期望{expectedLength}字节，实际{data.Length}字节";
                return frame;
            }

            // 提取数据内容
            if (frame.DataLength > 0)
            {
                frame.DataContent = new byte[frame.DataLength];
                Array.Copy(data, 5, frame.DataContent, 0, frame.DataLength);
            }

            // 提取CRC
            frame.CrcValue = (ushort)((data[data.Length - 2] << 8) | data[data.Length - 1]);

            // 验证CRC
            frame.IsValid = ValidateCrc(data);
            if (!frame.IsValid)
            {
                frame.ErrorMessage = "CRC校验失败";
            }

            return frame;
        }
        catch (Exception ex)
        {
            frame.IsValid = false;
            frame.ErrorMessage = $"解析异常: {ex.Message}";
            return frame;
        }
    }

    private static bool ValidateCrc(byte[] data)
    {
        // 计算CRC16校验
        var crcData = new byte[data.Length - 2];
        Array.Copy(data, 0, crcData, 0, crcData.Length);

        ushort calculatedCrc = CalculateCrc16(crcData);
        ushort frameCrc = (ushort)((data[data.Length - 2] << 8) | data[data.Length - 1]);

        return calculatedCrc == frameCrc;
    }

    private static ushort CalculateCrc16(byte[] data)
    {
        // CRC16-CCITT算法实现
        ushort crc = 0xFFFF;

        foreach (byte b in data)
        {
            crc ^= (ushort)(b << 8);
            for (int i = 0; i < 8; i++)
            {
                if ((crc & 0x8000) != 0)
                    crc = (ushort)((crc << 1) ^ 0x1021);
                else
                    crc <<= 1;
            }
        }

        return crc;
    }
}
```

### 6.2 设备发现服务
```csharp
public class DeviceDiscoveryService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<DeviceDiscoveryService> _logger;

    public DeviceDiscoveryService(IUnitOfWork unitOfWork, ILogger<DeviceDiscoveryService> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<List<Device>> DiscoverDevicesAsync(int unitId, byte startAddress = 0x00, byte endAddress = 0xFF)
    {
        var discoveredDevices = new List<Device>();
        var unit = await _unitOfWork.AirConditioningUnits.GetByIdAsync(unitId);

        if (unit == null)
        {
            throw new ArgumentException($"机组 {unitId} 不存在");
        }

        _logger.LogInformation("开始扫描机组 {UnitName} 的设备，地址范围：{StartAddress:X2}-{EndAddress:X2}",
            unit.UnitName, startAddress, endAddress);

        for (byte address = startAddress; address <= endAddress; address++)
        {
            try
            {
                var discoveryResult = await TryDiscoverDeviceAsync(unit, address);

                // 记录发现日志
                var discoveryLog = new DeviceDiscoveryLog
                {
                    DiscoveryTime = DateTime.UtcNow,
                    AirConditioningUnitId = unitId,
                    DeviceAddress = address,
                    Result = discoveryResult.Result,
                    ResultMessage = discoveryResult.Message,
                    IsNewDevice = discoveryResult.Device != null && discoveryResult.IsNewDevice
                };

                if (discoveryResult.Device != null)
                {
                    discoveryLog.DeviceType = discoveryResult.Device.DeviceType;
                    discoveryLog.ModelCode = discoveryResult.Device.ModelCode;
                    discoveryLog.FirmwareVersion = discoveryResult.Device.FirmwareVersion;
                    discoveryLog.DeviceId = discoveryResult.Device.Id;

                    discoveredDevices.Add(discoveryResult.Device);
                }

                await _unitOfWork.DeviceDiscoveryLogs.AddAsync(discoveryLog);

                // 每10个地址保存一次
                if (address % 10 == 0)
                {
                    await _unitOfWork.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "扫描地址 {Address:X2} 时发生异常", address);
            }
        }

        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("设备扫描完成，发现 {Count} 个设备", discoveredDevices.Count);
        return discoveredDevices;
    }

    private async Task<DeviceDiscoveryResult> TryDiscoverDeviceAsync(AirConditioningUnit unit, byte address)
    {
        // 检查设备是否已存在
        var existingDevice = await _unitOfWork.Devices
            .GetByAddressAsync(unit.Id, address);

        if (existingDevice != null)
        {
            // 更新最后通讯时间
            existingDevice.LastCommunicationTime = DateTime.UtcNow;
            existingDevice.IsOnline = true;
            existingDevice.CommunicationFailureCount = 0;

            await _unitOfWork.Devices.UpdateAsync(existingDevice);

            return new DeviceDiscoveryResult
            {
                Result = DiscoveryResult.Success,
                Message = "设备已存在",
                Device = existingDevice,
                IsNewDevice = false
            };
        }

        // 尝试通讯发现新设备
        // 这里应该调用实际的RS485通讯服务
        // 暂时模拟设备发现逻辑

        // 模拟：根据地址范围判断设备类型
        var deviceType = DetermineDeviceType(address);
        var deviceRole = DetermineDeviceRole(address);

        var newDevice = new Device
        {
            DeviceAddress = address,
            DeviceName = $"设备_{address:X2}",
            DeviceType = deviceType,
            DeviceRole = deviceRole,
            AirConditioningUnitId = unit.Id,
            FirstDiscoveredTime = DateTime.UtcNow,
            LastCommunicationTime = DateTime.UtcNow,
            IsOnline = true,
            CommunicationFailureCount = 0
        };

        // 如果是从设备，设置主设备关系
        if (deviceRole == DeviceRole.Slave && unit.MasterDeviceId.HasValue)
        {
            newDevice.MasterDeviceId = unit.MasterDeviceId;
        }

        await _unitOfWork.Devices.AddAsync(newDevice);

        return new DeviceDiscoveryResult
        {
            Result = DiscoveryResult.Success,
            Message = "发现新设备",
            Device = newDevice,
            IsNewDevice = true
        };
    }

    private DeviceType DetermineDeviceType(byte address)
    {
        // 根据地址范围判断设备类型的逻辑
        return address switch
        {
            >= 0xF0 and <= 0xFF => DeviceType.OutdoorUnit,  // F0-FF: 外机
            >= 0x00 and <= 0x0F => DeviceType.IndoorUnit,   // 00-0F: 内机
            >= 0x10 and <= 0x1F => DeviceType.WiredController, // 10-1F: 线控器
            >= 0x20 and <= 0x2F => DeviceType.CentralController, // 20-2F: 集控器
            _ => DeviceType.Other
        };
    }

    private DeviceRole DetermineDeviceRole(byte address)
    {
        // F1通常是主外机
        return address == 0xF1 ? DeviceRole.Master : DeviceRole.Slave;
    }
}

public class DeviceDiscoveryResult
{
    public DiscoveryResult Result { get; set; }
    public string Message { get; set; }
    public Device Device { get; set; }
    public bool IsNewDevice { get; set; }
}
```

### 6.3 通讯数据处理服务
```csharp
public class CommunicationDataProcessor
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CommunicationDataProcessor> _logger;

    public CommunicationDataProcessor(IUnitOfWork unitOfWork, ILogger<CommunicationDataProcessor> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task ProcessReceivedDataAsync(string serialPortSource, byte[] rawData)
    {
        try
        {
            // 解析RS485协议帧
            var frame = Rs485ProtocolParser.ParseFrame(rawData);

            // 查找对应的机组
            var unit = await _unitOfWork.AirConditioningUnits
                .GetBySerialPortSourceAsync(serialPortSource);

            if (unit == null)
            {
                _logger.LogWarning("未找到串口源 {SerialPortSource} 对应的机组", serialPortSource);
                return;
            }

            // 创建通讯日志
            var commLog = new CommunicationLog
            {
                Timestamp = DateTime.UtcNow,
                Direction = CommunicationDirection.SystemReceived,
                Type = DetermineCommunicationType(frame.CommandCode),
                FrameHeader = frame.FrameHeader,
                SourceAddress = frame.SourceAddress,
                TargetAddress = frame.TargetAddress,
                CommandCode = frame.CommandCode,
                DataLength = frame.DataLength,
                DataContent = frame.DataContent,
                CrcValue = frame.CrcValue,
                RawData = rawData,
                HexString = Convert.ToHexString(rawData),
                IsValidFrame = frame.IsValid,
                IsCrcValid = frame.IsValid,
                ErrorMessage = frame.ErrorMessage,
                AirConditioningUnitId = unit.Id
            };

            // 查找相关设备
            var device = await _unitOfWork.Devices
                .GetByAddressAsync(unit.Id, frame.SourceAddress);

            if (device != null)
            {
                commLog.DeviceId = device.Id;

                // 更新设备状态
                device.LastCommunicationTime = DateTime.UtcNow;
                device.LastResponseTime = DateTime.UtcNow;
                device.IsOnline = true;
                device.CommunicationFailureCount = 0;

                await _unitOfWork.Devices.UpdateAsync(device);
            }

            // 保存通讯日志
            await _unitOfWork.CommunicationLogs.AddAsync(commLog);

            // 如果是有效帧，解析参数数据
            if (frame.IsValid && device != null)
            {
                await ParseAndSaveParameterDataAsync(device, frame, commLog);
            }

            await _unitOfWork.SaveChangesAsync();

            // 更新机组最后数据接收时间
            unit.LastDataReceived = DateTime.UtcNow;
            unit.IsOnline = true;
            await _unitOfWork.AirConditioningUnits.UpdateAsync(unit);
            await _unitOfWork.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理通讯数据时发生异常");
        }
    }

    private CommunicationType DetermineCommunicationType(byte commandCode)
    {
        return commandCode switch
        {
            0x01 => CommunicationType.ParameterRead,
            0x02 => CommunicationType.ParameterWrite,
            0x03 => CommunicationType.StatusQuery,
            0x04 => CommunicationType.AlarmQuery,
            0x05 => CommunicationType.DeviceInfo,
            0x06 => CommunicationType.Heartbeat,
            _ => CommunicationType.Other
        };
    }

    private async Task ParseAndSaveParameterDataAsync(Device device, Rs485Frame frame, CommunicationLog commLog)
    {
        if (device.DeviceModelId == null || frame.DataContent == null || frame.DataContent.Length == 0)
            return;

        // 获取设备参数定义
        var parameters = await _unitOfWork.DeviceParameters
            .GetByDeviceModelAndCommandAsync(device.DeviceModelId.Value, frame.CommandCode);

        foreach (var parameter in parameters)
        {
            try
            {
                var value = ExtractParameterValue(frame.DataContent, parameter);

                var parameterValue = new DeviceParameterValue
                {
                    DeviceId = device.Id,
                    DeviceParameterId = parameter.Id,
                    Timestamp = DateTime.UtcNow,
                    Value = value.StringValue,
                    NumericValue = value.NumericValue,
                    BooleanValue = value.BooleanValue,
                    Quality = DataQuality.Good,
                    Source = "RS485",
                    CommunicationLogId = commLog.Id
                };

                await _unitOfWork.DeviceParameterValues.AddAsync(parameterValue);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析参数 {ParameterCode} 时发生异常", parameter.ParameterCode);
            }
        }
    }

    private (string StringValue, decimal? NumericValue, bool? BooleanValue) ExtractParameterValue(
        byte[] data, DeviceParameter parameter)
    {
        // 根据参数格式提取值
        return parameter.Format switch
        {
            ParameterFormat.SingleValue => ExtractSingleValue(data, parameter),
            ParameterFormat.BitField => ExtractBitField(data, parameter),
            ParameterFormat.ByteCombination => ExtractByteCombination(data, parameter),
            ParameterFormat.MixedFormat => ExtractMixedFormat(data, parameter),
            ParameterFormat.EnumValue => ExtractEnumValue(data, parameter),
            _ => (data.ToString(), null, null)
        };
    }

    private (string, decimal?, bool?) ExtractSingleValue(byte[] data, DeviceParameter parameter)
    {
        if (data.Length < parameter.ParameterIndex + 2)
            return ("", null, null);

        var value = (data[parameter.ParameterIndex] << 8) | data[parameter.ParameterIndex + 1];
        return (value.ToString(), value, null);
    }

    private (string, decimal?, bool?) ExtractBitField(byte[] data, DeviceParameter parameter)
    {
        if (data.Length < parameter.ParameterIndex + 2)
            return ("", null, null);

        var value = (data[parameter.ParameterIndex] << 8) | data[parameter.ParameterIndex + 1];
        var bitValue = (value & (1 << parameter.BitPosition)) != 0;
        return (bitValue.ToString(), null, bitValue);
    }

    private (string, decimal?, bool?) ExtractByteCombination(byte[] data, DeviceParameter parameter)
    {
        if (data.Length < parameter.ParameterIndex + 2)
            return ("", null, null);

        var highByte = data[parameter.ParameterIndex];
        var lowByte = data[parameter.ParameterIndex + 1];
        var combinedValue = $"{highByte},{lowByte}";
        return (combinedValue, null, null);
    }

    private (string, decimal?, bool?) ExtractMixedFormat(byte[] data, DeviceParameter parameter)
    {
        // 混合格式的具体实现根据实际协议定义
        return ExtractSingleValue(data, parameter);
    }

    private (string, decimal?, bool?) ExtractEnumValue(byte[] data, DeviceParameter parameter)
    {
        if (data.Length < parameter.ParameterIndex + 1)
            return ("", null, null);

        var enumValue = data[parameter.ParameterIndex];
        return (enumValue.ToString(), enumValue, null);
    }
}
```

## 7. 修正总结

### ✅ **重新设计完成的关键修正**

**1. 系统架构准确反映**
- ✅ 云端IoT监控系统 + 本地Windows服务推送数据
- ✅ 新增 `AirConditioningUnit` 实体作为设备拓扑根节点
- ✅ 新增 `SerialPortSource` 字段标识数据来源

**2. 设备拓扑结构正确建模**
- ✅ 机组 -> 主外机 -> 子设备的层次结构
- ✅ 主从设备关系通过 `MasterDeviceId` 和 `DeviceRole` 表示
- ✅ 设备类型细分：外机、内机、线控器、集控器等

**3. RS485通讯协议精确实现**
- ✅ 协议格式：7E + 源地址 + 目标地址 + 命令码 + 长度 + 数据 + CRC
- ✅ 新增 `Rs485Frame` 实体和解析器
- ✅ 新增 `CommunicationLog` 实体存储通讯数据

**4. 动态设备发现机制**
- ✅ 基于实际响应识别存在的设备
- ✅ 新增 `DeviceDiscoveryLog` 记录发现过程
- ✅ 支持设备在线/离线状态检测

**5. 通讯数据处理完整流程**
- ✅ `CommunicationDataProcessor` 处理接收到的数据
- ✅ 自动解析协议帧并更新设备状态
- ✅ 提取参数值并存储到数据库

这个重新设计的数据模型完全符合您描述的实际RS485通讯架构，准确反映了空调机组的真实通讯模式！
```
```
```
